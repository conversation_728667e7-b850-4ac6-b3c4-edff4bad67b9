#!/bin/bash

declare -a games=(
    'g1001,24,0,["m1001_1", "m1001_2", "m1001_3", "m1001_4", "m1001_5"]'
    'g1002,8,0,["m1002_1", "m1002_2", "m1002_3", "m1002_4", "m1002_5", "m1002_6", "m1002_7", "m1002_8", "m1002_9"]'
    'g1003,24,4,["m1003_1"]'
    'g1007,12,0,["m1007"]'
    'g1008,16,4,["m1008_1", "m1008_2", "m1008_3", "m1008_4"]'
    'g1009,12,0,["m1009"]'
    'g1010,8,0,["m1010"]'
    'g1011,8,2,["m1011_1", "m1011_2", "m1011_3", "m1011_4"]'
    'g1012,12,2,["m1012"]'
    'g1013,25,0,["m1013"]'
    'g1014,30,0,["m1014"]'
    'g1015,10,0,["m1015"]'
    'g1016,30,0,["m1016"]'
    'g1017,15,0,["m1017"]'
    'g1018,16,4,["m1018_1", "m1018_2", "m1018_3", "m1018_4"]'
    'g1019,10,0,["m1019"]'
    'g1020,10,0,["m1020"]'
    'g1021,12,0,["m1021_1", "m1021_2", "m1021_3", "m1021_4"]'
    'g1022,16,2,["m1022"]'
    'g1023,8,0,["m1023"]'
    'g1024,32,4,["m1024"]'
    'g1025,10,0,["m1025"]'
    'g1026,30,0,["m1026_1", "m1026_2"]'
    'g1027,16,4,["m1027"]'
    'g1028,16,0,["m1028"]'
    'g1029,16,4,["m1029_1", "m1029_2"]'
    'g1030,12,2,["m1030_1", "m1030_2", "m1030_3"]'
    'g1031,6,0,["m1031"]'
    'g1032,30,0,["m1032"]'
    'g1033,12,2,["m1033"]'
    'g1036,18,2,["m1036_1", "m1036_2"]'
    'g1046,30,0,["m1046"]'
)

for entry in "${games[@]}"; do
    IFS=',' read -r gameId maxPlayers teamNumber rest <<< "$entry"

    maps="${entry#*,*,*,}"
    
    json=$(cat <<EOF
{
  "gameId": "$gameId",
  "maxPlayers": $maxPlayers,
  "teamNumber": $teamNumber,
  "maps": $maps
}
EOF
    )

    echo "$json" > "./$gameId.json"
done

echo "✅ All JSON files generated cleanly in /"
