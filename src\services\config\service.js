const ConfigManager = require("@common/ConfigManager");
const Responses = require("@common/Responses");
const logger = require("@common/Logger");

async function getConfigFile(configName) {
    try {
        logger.info(`Fetching config file: ${configName}`);
        
        // Use ConfigManager to fetch from CDN
        const configData = await ConfigManager.get(configName);
        
        if (!configData) {
            logger.warn(`Config file not found: ${configName}`);
            return Responses.notFound();
        }

        return Responses.success(configData);
    } catch (error) {
        logger.error(`Error fetching config file ${configName}:`, error);
        return Responses.innerError();
    }
}

module.exports = {
    getConfigFile: getConfigFile
};
