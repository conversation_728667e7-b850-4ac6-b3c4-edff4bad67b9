const Responses = require("@common/Responses");
const service = require("@config-service/service");

async function getConfigFile(request) {
    const configName = request.params.configName;
    if (!configName) {
        return Responses.invalidParameter();
    }

    return await service.getConfigFile(configName);
}

module.exports = [
    {
        "path": "/config/files/:configName",
        "methods": ["GET"],
        "functions": [getConfigFile]
    }
];
